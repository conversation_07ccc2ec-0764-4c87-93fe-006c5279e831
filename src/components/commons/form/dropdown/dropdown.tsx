import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@base/select.tsx";
import { Label } from "@base/label.tsx";
import type { DropdownProps } from "@commons/form/dropdown/types.ts";
import { cn } from "@lib/utils.ts";

const Dropdown: React.FC<DropdownProps> = ({
  id,
  label,
  placeholder = "เลือก...",
  value,
  options,
  error,
  disabled = false,
  required = false,
  className = "",
  onValueChange,
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <Label htmlFor={id} className="block text-sm font-medium">
          {label}
          {required && <span className="ml-1 text-red-500">*</span>}
        </Label>
      )}

      <Select value={value} onValueChange={onValueChange} disabled={disabled}>
        <SelectTrigger
          id={id}
          className={cn(
            error ? "border-red-500" : "",
            "hover:border-primary focus:border-primary data-[state=open]:border-primary w-full border-gray-300 bg-white",
          )}
        >
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent className="border-none bg-white">
          {options.map((option) => (
            <SelectItem
              key={option.value}
              value={option.value}
              disabled={option.disabled}
              className="hover:bg-primary/5 cursor-pointer"
            >
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {error && <p className="mt-1 text-xs text-red-600">{error}</p>}
    </div>
  );
};

export default Dropdown;
