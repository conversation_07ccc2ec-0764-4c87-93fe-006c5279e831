import React from "react";
import { Controller } from "react-hook-form";
import { Link } from "react-router-dom";
import { InputField } from "@/components/commons/form/input-field";
import type { ContactInfoProps } from "./types";
import { getContactInfoErrorMessage } from "./helpers";

const ContactInfo: React.FC<ContactInfoProps> = ({
  control,
  errors,
  isAuthenticated,
}) => {
  return (
    <>
      <div className="item-center flex justify-between">
        <h2 className="text-[23px] font-medium text-black">ข้อมูลติดต่อ</h2>
        {!isAuthenticated && (
          <Link to="/login" className="text-sm text-black hover:underline">
            Log in ?
          </Link>
        )}
      </div>
      <Controller
        name="contactInfo.email"
        control={control}
        render={({ field }) => (
          <InputField
            id="email"
            type="email"
            placeholder="Email"
            error={getContactInfoErrorMessage(errors.contactInfo?.email)}
            {...field}
          />
        )}
      />
    </>
  );
};

export default ContactInfo;
