import React, { useState } from "react";
import { Controller } from "react-hook-form";
import { RadioGroup, RadioGroupItem } from "@base/radio-group";
import { Label } from "@base/label";
import { Badge } from "@commons/badge";
import { useAddresses } from "@hooks/useAddresses";
import { AddressForm } from "@components/address/address-form";
import type { AddressFormData } from "@/types/address";
import { AddAddressButton } from "@components/address";
import { useTranslation } from "@hooks/useTranslation";
import type { ShippingAddressProps } from "./types";
import {
  handleCardClick,
  mapAddressToCheckoutForm,
  clearShippingAddressForm,
} from "./helpers";

const ShippingAddress: React.FC<ShippingAddressProps> = ({
  control,
  setValue,
}) => {
  const { t } = useTranslation();
  const { addresses, isLoading, addAddress } = useAddresses();
  const [selectedAddressId, setSelectedAddressId] = useState<string>("new");
  const [isFormOpen, setIsFormOpen] = useState(false);

  const handleAddressSelect = (addressId: string) => {
    setSelectedAddressId(addressId);

    if (addressId !== "new") {
      const selectedAddress = addresses.find(
        (addr) => addr.id.toString() === addressId,
      );
      if (selectedAddress) {
        mapAddressToCheckoutForm(selectedAddress, setValue);
      }
    } else {
      clearShippingAddressForm(setValue);
    }
  };

  const handleOpenAddressForm = () => {
    setIsFormOpen(true);
  };

  const handleAddAddress = async (data: AddressFormData) => {
    try {
      await addAddress(data);
      setIsFormOpen(false);
    } catch (error) {
      console.error("Error adding address:", error);
      throw error;
    }
  };

  return (
    <>
      <h2 className="text-[23px] font-medium text-black">ที่อยู่จัดส่ง</h2>

      {!isLoading && addresses.length > 0 && (
        <div className="space-y-3">
          <Controller
            name="shippingAddress"
            control={control}
            render={() => (
              <RadioGroup
                value={selectedAddressId}
                onValueChange={handleAddressSelect}
                className="space-y-3"
              >
                {addresses.map((address) => (
                  <div
                    key={address.id}
                    className={`border-primary hover:border-primary/80 hover:bg-primary/5 group min-h-[100px] w-full cursor-pointer rounded-[16px] border p-4 transition-colors duration-200 ${
                      selectedAddressId === address.id.toString()
                        ? "border-primary bg-primary/5"
                        : ""
                    }`}
                    onClick={(e) =>
                      handleCardClick(
                        e,
                        handleAddressSelect,
                        address.id.toString(),
                      )
                    }
                  >
                    <div className="flex items-start space-x-3">
                      <RadioGroupItem
                        value={address.id.toString()}
                        id={`address-${address.id}`}
                        className="mt-1"
                      />
                      <Label
                        htmlFor={`address-${address.id}`}
                        className="flex-1 cursor-pointer"
                      >
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-black">
                              {address.recipient_name}
                            </span>
                            {address.is_default && (
                              <Badge variant="outline" className="text-xs">
                                {t("address.main")}
                              </Badge>
                            )}
                          </div>
                          <div className="text-sm text-gray-600">
                            {address.full_address}
                          </div>
                          {address.delivery_instructions && (
                            <div className="text-xs text-gray-500">
                              หมายเหตุ: {address.delivery_instructions}
                            </div>
                          )}
                        </div>
                      </Label>
                    </div>
                  </div>
                ))}
              </RadioGroup>
            )}
          />
        </div>
      )}

      <AddAddressButton onClick={handleOpenAddressForm} />

      <AddressForm
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSubmit={handleAddAddress}
        isEdit={false}
      />
    </>
  );
};

export default ShippingAddress;
