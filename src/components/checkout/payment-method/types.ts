import type { Control, FieldErrors, UseFormWatch } from "react-hook-form";

import type { CheckoutFormData } from "@/pages/checkout/constants";
import type { BasePaymentMethod } from "@/types/payment-methods";

export interface PaymentMethodProps {
  control: Control<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
  watch: UseFormWatch<CheckoutFormData>;
}

export interface PaymentOption {
  id: string;
  type: string;
  name: string;
  description?: string;
  icon?: string;
  savedMethod?: BasePaymentMethod;
}
