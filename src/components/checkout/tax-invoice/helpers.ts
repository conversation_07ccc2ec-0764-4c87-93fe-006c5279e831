import { gsap } from "gsap";
import type { UseFormSetValue, UseFormWatch } from "react-hook-form";

import type { CheckoutFormData } from "@/pages/checkout/constants";

/**
 * Helper function to safely get error message from form errors
 */
export const getErrorMessage = (error: unknown): string | undefined => {
  if (!error) return undefined;
  if (typeof error === "object" && error !== null && "message" in error) {
    const errorObj = error as { message?: string };
    return errorObj.message;
  }
  return undefined;
};

export const copyShippingToTax = (
  watch: UseFormWatch<CheckoutFormData>,
  setValue: UseFormSetValue<CheckoutFormData>,
) => {
  const shippingAddress = watch("shippingAddress");
  setValue("taxInvoice.taxInfo.address_line1", shippingAddress.address_line1);
  setValue("taxInvoice.taxInfo.address_line2", shippingAddress.address_line2);
  setValue("taxInvoice.taxInfo.subdistrict", shippingAddress.subdistrict);
  setValue("taxInvoice.taxInfo.district", shippingAddress.district);
  setValue("taxInvoice.taxInfo.city", shippingAddress.city);
  setValue("taxInvoice.taxInfo.province", shippingAddress.province);
  setValue("taxInvoice.taxInfo.postal_code", shippingAddress.postal_code);
  setValue("taxInvoice.taxInfo.country", shippingAddress.country);
};

export const animateCardExpand = (element: HTMLElement) => {
  gsap.set(element, {
    height: 0,
    opacity: 0,
    overflow: "hidden",
    transformOrigin: "top center",
  });

  gsap.to(element, {
    height: "auto",
    opacity: 1,
    duration: 0.6,
    ease: "power2.out",
    onComplete: () => {
      gsap.set(element, { overflow: "visible" });
    },
  });
};

export const animateCardCollapse = (
  element: HTMLElement,
  onComplete?: () => void,
) => {
  gsap.set(element, { overflow: "hidden" });

  gsap.to(element, {
    height: 0,
    opacity: 0,
    duration: 0.4,
    ease: "power2.in",
    onComplete,
  });
};

export const handleCardClick = (
  e: React.MouseEvent<HTMLDivElement>,
  onChange: (value: boolean) => void,
  value: boolean,
) => {
  // Prevent card click when clicking on form elements
  const target = e.target as HTMLElement;
  if (
    target.tagName === "INPUT" ||
    target.tagName === "SELECT" ||
    target.tagName === "TEXTAREA" ||
    target.tagName === "BUTTON" ||
    target.closest(
      'input, select, textarea, button, [role="combobox"], [role="listbox"]',
    )
  ) {
    return;
  }
  onChange(value);
};
