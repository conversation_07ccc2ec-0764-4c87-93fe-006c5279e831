import React from "react";

/**
 * Helper function to handle card clicks without interfering with form elements
 * Prevents double-triggering when clicking on radio buttons or other form elements
 */
export const handleCardClick = (
  e: React.MouseEvent<HTMLDivElement>,
  onChange: (value: string) => void,
  value: string,
) => {
  // Prevent card click when clicking on form elements
  const target = e.target as HTMLElement;
  if (
    target.tagName === "INPUT" ||
    target.tagName === "SELECT" ||
    target.tagName === "TEXTAREA" ||
    target.tagName === "BUTTON" ||
    target.closest(
      'input, select, textarea, button, [role="combobox"], [role="listbox"]',
    )
  ) {
    return;
  }
  onChange(value);
};
