import React from "react";
import { Controller } from "react-hook-form";
import { Card, CardContent } from "@base/card";
import { RadioGroup, RadioGroupItem } from "@base/radio-group";
import { Label } from "@base/label";
import { SHIPPING_METHODS } from "@/components/checkout/tax-invoice/constants";
import { useTranslation } from "@hooks/useTranslation.ts";
import type { ShippingMethodProps } from "./types";
import { handleCardClick } from "./helpers";

const ShippingMethod: React.FC<ShippingMethodProps> = ({ control, errors }) => {
  const { t } = useTranslation();

  return (
    <div>
      <h2 className="mb-4 text-[23px] font-medium text-black">
        {t("shippingMethod.title")}
      </h2>

      <div className="space-y-4">
        <Controller
          name="shippingMethod"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value}
              onValueChange={(value) => {
                field.onChange(value);
              }}
            >
              {SHIPPING_METHODS.map((method) => (
                <Card
                  key={method.id}
                  className={`cursor-pointer transition-colors ${
                    field.value === method.id
                      ? "border-primary bg-primary/5"
                      : "border-primary-border-card"
                  }`}
                  onClick={(e) => handleCardClick(e, field.onChange, method.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem value={method.id} id={method.id} />
                      <div className="flex flex-1 items-center space-x-3">
                        <img
                          src={method.icon}
                          alt={method.name}
                          className="h-12 w-12 rounded-lg object-contain"
                        />
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <Label
                              htmlFor={method.id}
                              className="cursor-pointer font-medium"
                            >
                              {method.name}
                            </Label>
                            <span className="font-medium">
                              ฿{method.price.toLocaleString()}
                            </span>
                          </div>
                          <p className="mt-1 text-sm text-gray-500">
                            {method.estimatedDays}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </RadioGroup>
          )}
        />
        {errors.shippingMethod && (
          <p className="mt-2 text-xs text-red-600">
            {errors.shippingMethod.message}
          </p>
        )}
      </div>
    </div>
  );
};

export default ShippingMethod;
