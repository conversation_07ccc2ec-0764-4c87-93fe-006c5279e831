import { useTranslation } from "@hooks/useTranslation.ts";
import React, { useEffect } from "react";
import QuantitySelector from "@base/quantity-selector.tsx";
import { Trash2 } from "lucide-react";
import {
  useCartItems,
  useDeleteFromCart,
  useUpdateCartItem,
} from "@services/api/auth/cart/cart-service.ts";
import { useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";

const ShoppingCartPage = () => {
  const { t } = useTranslation();
  const { data: cartsResponse, error, isError, isLoading } = useCartItems();
  useEffect(() => {
    console.log(cartsResponse);
  }, [cartsResponse]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isError) {
    return <div>Error: {error.message}</div>;
  }
  const carts = cartsResponse!.results;
  const totalPrice = carts.reduce((total, item) => {
    return total + item.quantity * item.product_variant.price;
  }, 0);

  return (
    <>
      <div className="px-4 pt-24 pb-12 lg:px-20 lg:pt-32">
        <h1 className={`mb-6 text-[2rem]`}>{t("cart.title")}</h1>
        <hr className={`mb-4`} />
        <div className="flex flex-col lg:flex-row lg:justify-between lg:gap-16">
          <div className="lg:w-[120rem] xl:w-[200rem]">
            <div className="mb-8">
              {carts.map((cart) => (
                <CartItem
                  key={cart.id}
                  id={cart.id}
                  name={cart.product_variant.product_name}
                  amount={cart.quantity}
                  variant={cart.product_variant.sku}
                  variantId={cart.product_variant.id}
                  price={cart.product_variant.price}
                  image={cart.product_variant.first_image_url}
                />
              ))}
            </div>
          </div>
          <div className="">
            <CheckOut totalPrice={totalPrice} />
          </div>
        </div>
      </div>
    </>
  );
};

interface CartProps {
  id: number;
  amount: number;
  variant: string;
  variantId: number;
  name: string;
  price: number;
  image: string;
}

const CartItem: React.FC<CartProps> = ({
  id,
  name,
  image,
  variant,
  variantId,
  price,
  amount,
}) => {
  const queryClient = useQueryClient();
  const invalidateCart = () => {
    queryClient.invalidateQueries({ queryKey: ["cart-items"] });
  };
  const updateCart = useUpdateCartItem(invalidateCart);
  const deleteFromCart = useDeleteFromCart(invalidateCart);

  const handleMinus = (amount: number) => {
    if (amount <= 1) return;
    updateCart.mutate({
      id,
      request: {
        product_variant_id: variantId,
        quantity: amount - 1,
      },
    });
  };

  const handlePlus = (amount: number) => {
    updateCart.mutate({
      id,
      request: {
        product_variant_id: variantId,
        quantity: amount + 1,
      },
    });
  };

  const handleDelete = () => {
    deleteFromCart.mutate(id);
  };

  return (
    <>
      <div className={`flex flex-row items-center`}>
        <div className="mr-4 h-[6rem] w-[6rem] md:h-[10rem] md:w-[10rem] xl:h-[14rem] xl:w-[14rem]">
          <img
            src={image}
            alt={name}
            className="h-full w-full rounded-lg object-cover transition-transform duration-300 ease-in-out hover:scale-105"
          />
        </div>
        <div className={`flex w-full flex-col`}>
          <div className="mb-1">
            <h3 className="text-primary-dark truncate text-[16px] font-semibold">
              {name}
            </h3>
          </div>
          <div className={`mb-2 flex flex-row items-center justify-between`}>
            <div>
              <a className="text-normal rounded-lg border-1 border-[#E3CAA5] px-2 py-1 text-[12px]">
                {variant}
              </a>
            </div>
            <div>
              <h3 className="text-[16px] font-bold">
                ฿{formatNumber(price * amount)}
              </h3>
            </div>
          </div>
          <div className="row flex items-center justify-between">
            <QuantitySelector
              amount={amount}
              isEnabled={true}
              handlePlusClick={handlePlus}
              handleMinusClick={handleMinus}
            />
            <div>
              <button className="cursor-point" onClick={() => handleDelete()}>
                <Trash2 />
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

function formatNumber(number: number) {
  return new Intl.NumberFormat("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(number);
}

interface CheckOutProps {
  totalPrice: number;
}

const CheckOut: React.FC<CheckOutProps> = ({ totalPrice }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const handleCheckOut = () => {
    navigate("/checkout");
  };
  return (
    <>
      <div className="bg-secondary-light flex w-full flex-col rounded-xl px-3 py-2 shadow-lg lg:w-[12rem] xl:w-[20rem]">
        <div className="flex flex-row items-center justify-between text-[1.3rem] font-bold lg:text-[1rem] xl:text-[1.2rem]">
          <h2 className="">{t("cart.total")}</h2>
          <h2>{formatNumber(totalPrice)}฿</h2>
        </div>
        <hr className="mb-4" />
        <div className="mb-3">
          <button
            onClick={handleCheckOut}
            className="bg-primary border-primary w-full cursor-pointer rounded-lg border-1 py-2 text-white"
          >
            {t("cart.checkOut")}
          </button>
        </div>
        <div>
          <p className="text-[16px]">{t("cart.tax")}</p>
        </div>
      </div>
    </>
  );
};

export default ShoppingCartPage;
